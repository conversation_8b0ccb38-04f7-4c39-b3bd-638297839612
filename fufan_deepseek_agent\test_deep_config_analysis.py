#!/usr/bin/env python3
"""
深度分析项目中所有可能的硬编码配置
"""
import sys
import os
import re
from pathlib import Path
sys.path.append('llm_backend')

def scan_hardcoded_configs():
    """扫描项目中的硬编码配置"""
    print("🔍 深度扫描项目中的硬编码配置...")
    print("=" * 80)
    
    # 定义要扫描的文件类型和模式
    file_patterns = ['*.py', '*.yaml', '*.yml', '*.json', '*.md', '*.txt']
    
    # 定义敏感信息的正则模式
    sensitive_patterns = {
        'API Keys': [
            r'sk-[a-zA-Z0-9]{20,}',  # OpenAI/DeepSeek API keys
            r'api_key\s*[=:]\s*["\'][^"\']+["\']',  # api_key = "xxx"
        ],
        'Database Passwords': [
            r'password\s*[=:]\s*["\'][^"\']+["\']',  # password = "xxx"
            r'DB_PASSWORD\s*[=:]\s*[^#\n]+',  # DB_PASSWORD=xxx
            r'NEO4J_PASSWORD\s*[=:]\s*[^#\n]+',  # NEO4J_PASSWORD=xxx
        ],
        'Database Connections': [
            r'mysql://[^"\s]+',  # MySQL connection strings
            r'bolt://[^"\s]+',   # Neo4j connection strings
            r'redis://[^"\s]+',  # Redis connection strings
        ],
        'Hardcoded IPs/URLs': [
            r'192\.168\.\d+\.\d+',  # Local IP addresses
            r'localhost:\d+',       # localhost with port
            r'127\.0\.0\.1:\d+',    # 127.0.0.1 with port
        ]
    }
    
    # 要排除的文件和目录
    exclude_patterns = [
        '__pycache__',
        '.git',
        'node_modules',
        '.env.example',  # 示例文件允许有占位符
        'test_deep_config_analysis.py',  # 排除自己
        'test_all_configs_unified.py',   # 排除分析脚本
    ]
    
    findings = {}
    
    def should_exclude(file_path):
        """检查文件是否应该被排除"""
        path_str = str(file_path)
        return any(pattern in path_str for pattern in exclude_patterns)
    
    def scan_file(file_path):
        """扫描单个文件"""
        if should_exclude(file_path):
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except:
            return []
        
        file_findings = []
        
        for category, patterns in sensitive_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    # 获取匹配行号
                    line_num = content[:match.start()].count('\n') + 1
                    matched_text = match.group()
                    
                    # 过滤一些明显的误报
                    if 'example' in matched_text.lower() or 'placeholder' in matched_text.lower():
                        continue
                    if 'your-' in matched_text.lower() or 'xxx' in matched_text.lower():
                        continue
                    
                    file_findings.append({
                        'category': category,
                        'line': line_num,
                        'text': matched_text,
                        'pattern': pattern
                    })
        
        return file_findings
    
    # 扫描项目目录
    project_root = Path('.')
    
    for file_path in project_root.rglob('*'):
        if file_path.is_file() and any(file_path.match(pattern) for pattern in file_patterns):
            file_findings = scan_file(file_path)
            if file_findings:
                findings[str(file_path)] = file_findings
    
    # 输出结果
    if not findings:
        print("✅ 未发现明显的硬编码敏感配置")
        return True
    
    print("⚠️  发现以下硬编码配置:")
    
    for file_path, file_findings in findings.items():
        print(f"\n📁 {file_path}:")
        
        # 按类别分组
        by_category = {}
        for finding in file_findings:
            category = finding['category']
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(finding)
        
        for category, category_findings in by_category.items():
            print(f"  🔸 {category}:")
            for finding in category_findings:
                # 隐藏敏感信息
                display_text = finding['text']
                if len(display_text) > 50:
                    display_text = display_text[:20] + "..." + display_text[-10:]
                print(f"    行 {finding['line']}: {display_text}")
    
    return False

def check_config_consistency():
    """检查配置一致性"""
    print("\n" + "=" * 80)
    print("🔍 检查配置一致性...")
    print("=" * 80)
    
    # 检查主配置文件
    main_env = Path('llm_backend/.env')
    if not main_env.exists():
        print("❌ 主配置文件不存在")
        return False
    
    with open(main_env, 'r', encoding='utf-8') as f:
        main_content = f.read()
    
    # 检查其他可能的配置文件
    other_configs = [
        '.env.example',
        'llm_backend/app/graphrag/data/.env',
    ]
    
    inconsistencies = []
    
    for config_file in other_configs:
        config_path = Path(config_file)
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取配置项
            main_configs = re.findall(r'^([A-Z_]+)=', main_content, re.MULTILINE)
            other_configs_items = re.findall(r'^([A-Z_]+)=', content, re.MULTILINE)
            
            # 检查是否有配置项不在主文件中
            missing_in_main = set(other_configs_items) - set(main_configs)
            if missing_in_main:
                inconsistencies.append({
                    'file': config_file,
                    'missing': list(missing_in_main)
                })
    
    if inconsistencies:
        print("⚠️  发现配置不一致:")
        for item in inconsistencies:
            print(f"  📁 {item['file']} 中有主配置文件缺少的配置项:")
            for config in item['missing']:
                print(f"    - {config}")
        return False
    else:
        print("✅ 配置文件一致性良好")
        return True

def check_default_values():
    """检查config.py中的默认值"""
    print("\n" + "=" * 80)
    print("🔍 检查config.py中的默认值...")
    print("=" * 80)
    
    config_file = Path('llm_backend/app/core/config.py')
    if not config_file.exists():
        print("❌ config.py文件不存在")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查可能的硬编码默认值
    default_patterns = [
        r':\s*str\s*=\s*["\'][^"\']+["\']',  # str = "default_value"
        r':\s*int\s*=\s*\d+',               # int = 123
        r'password["\']?\s*=\s*["\'][^"\']+["\']',  # password = "xxx"
    ]
    
    issues = []
    for pattern in default_patterns:
        matches = re.finditer(pattern, content, re.IGNORECASE)
        for match in matches:
            line_num = content[:match.start()].count('\n') + 1
            matched_text = match.group()
            
            # 过滤一些正常的默认值
            if any(safe in matched_text.lower() for safe in ['localhost', 'neo4j', 'hs256', 'utf-8']):
                continue
            
            issues.append({
                'line': line_num,
                'text': matched_text.strip()
            })
    
    if issues:
        print("⚠️  发现可能的硬编码默认值:")
        for issue in issues:
            print(f"  行 {issue['line']}: {issue['text']}")
        return False
    else:
        print("✅ config.py中的默认值检查通过")
        return True

def provide_fix_suggestions():
    """提供修复建议"""
    print("\n" + "=" * 80)
    print("💡 修复建议")
    print("=" * 80)
    
    suggestions = [
        "1. 将所有硬编码的API密钥移动到 llm_backend/.env 文件",
        "2. 将所有硬编码的数据库密码移动到 llm_backend/.env 文件",
        "3. 使用环境变量替换代码中的硬编码IP地址和端口",
        "4. 在config.py中移除敏感信息的默认值",
        "5. 确保测试文件使用配置而不是硬编码值",
        "6. 更新文档中的示例，使用占位符而不是真实值",
        "7. 添加.env文件到.gitignore确保不被提交",
        "8. 为生产环境创建单独的配置文件模板"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")

def main():
    """主函数"""
    print("🚀 开始深度配置分析...")
    
    # 扫描硬编码配置
    no_hardcoded = scan_hardcoded_configs()
    
    # 检查配置一致性
    consistent = check_config_consistency()
    
    # 检查默认值
    good_defaults = check_default_values()
    
    # 提供建议
    provide_fix_suggestions()
    
    # 总结
    print("\n" + "=" * 80)
    print("🎯 深度配置分析结果")
    print("=" * 80)
    
    if no_hardcoded and consistent and good_defaults:
        print("🎉 配置管理优秀！")
        print("✅ 无硬编码敏感信息")
        print("✅ 配置文件一致")
        print("✅ 默认值安全")
    else:
        print("⚠️  发现配置问题:")
        if not no_hardcoded:
            print("  - 存在硬编码敏感配置")
        if not consistent:
            print("  - 配置文件不一致")
        if not good_defaults:
            print("  - 存在不安全的默认值")
    
    print("\n💡 建议: 修复发现的问题后重新运行此脚本")

if __name__ == "__main__":
    main()
