#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试服务器启动
"""

import os
import sys
from pathlib import Path

# 从.env文件加载环境变量
from dotenv import load_dotenv
load_dotenv()

def test_imports():
    """测试导入"""
    print("=" * 60)
    print("测试导入")
    print("=" * 60)
    
    try:
        print("1. 测试FastAPI...")
        from fastapi import FastAPI
        print("✓ FastAPI导入成功")
        
        print("2. 测试RAGChatService...")
        from app.services.rag_chat_service import RAGChatService
        print("✓ RAGChatService导入成功")
        
        print("3. 测试配置...")
        from app.core.config import settings
        print("✓ settings导入成功")
        
        print("4. 测试服务...")
        from app.services.llm_factory import LLMFactory
        print("✓ LLMFactory导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_components():
    """测试main.py的主要组件"""
    print("\n" + "=" * 60)
    print("测试main.py组件")
    print("=" * 60)
    
    try:
        print("1. 测试BaseModel...")
        from pydantic import BaseModel
        print("✓ BaseModel导入成功")
        
        print("2. 测试类型...")
        from typing import List, Dict, Optional
        print("✓ 类型导入成功")
        
        print("3. 测试FastAPI组件...")
        from fastapi import FastAPI, HTTPException, UploadFile, File, Form
        from fastapi.responses import StreamingResponse
        print("✓ FastAPI组件导入成功")
        
        print("4. 创建简单的FastAPI应用...")
        app = FastAPI()
        
        class TestRequest(BaseModel):
            message: str
        
        @app.get("/test")
        async def test_endpoint():
            return {"status": "ok"}
        
        print("✓ FastAPI应用创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ main.py组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rag_endpoint():
    """测试RAG端点定义"""
    print("\n" + "=" * 60)
    print("测试RAG端点定义")
    print("=" * 60)
    
    try:
        from fastapi import FastAPI
        from pydantic import BaseModel
        from typing import List, Dict
        from app.services.rag_chat_service import RAGChatService
        
        app = FastAPI()
        
        class RAGChatRequest(BaseModel):
            messages: List[Dict[str, str]]
            index_id: str
            user_id: int
        
        @app.post("/chat-rag")
        async def rag_chat_endpoint(request: RAGChatRequest):
            """基于文档的问答接口"""
            rag_chat_service = RAGChatService()
            return {"status": "ok", "message": "RAG端点定义成功"}
        
        print("✓ RAG端点定义成功")
        return True
        
    except Exception as e:
        print(f"✗ RAG端点定义失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 服务器启动问题诊断")
    
    test_results = []
    
    # 1. 测试导入
    result1 = test_imports()
    test_results.append(("基础导入", result1))
    
    # 2. 测试main.py组件
    result2 = test_main_components()
    test_results.append(("main.py组件", result2))
    
    # 3. 测试RAG端点
    result3 = test_rag_endpoint()
    test_results.append(("RAG端点定义", result3))
    
    # 总结
    print("\n" + "=" * 60)
    print("诊断结果")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(test_results)} 项测试通过")
    
    if success_count == len(test_results):
        print("\n🎉 所有组件测试通过！")
        print("✅ 服务器应该可以正常启动")
        print("\n📋 建议:")
        print("1. 尝试启动服务器: python -c \"import uvicorn; uvicorn.run('main:app', host='0.0.0.0', port=8000)\"")
        print("2. 或者使用: uvicorn main:app --host 0.0.0.0 --port 8000")
    else:
        print("\n⚠️  发现问题，需要进一步修复")
        
        if not test_results[0][1]:
            print("🔧 基础导入有问题 - 检查依赖包安装")
        if not test_results[1][1]:
            print("🔧 main.py组件有问题 - 检查FastAPI配置")
        if not test_results[2][1]:
            print("🔧 RAG端点有问题 - 检查RAGChatService")

if __name__ == "__main__":
    main()
