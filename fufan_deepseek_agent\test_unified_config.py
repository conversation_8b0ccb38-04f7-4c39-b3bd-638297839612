#!/usr/bin/env python3
"""
测试统一配置是否正确
"""
import sys
import os
sys.path.append('llm_backend')

from app.core.config import settings

def test_unified_config():
    """测试统一配置"""
    print("🔧 测试统一OpenAI API Key配置...")
    print("=" * 60)
    
    # 检查环境变量配置
    print("📋 环境变量配置检查:")
    
    # 检查VISION_API_KEY
    if settings.VISION_API_KEY:
        print(f"✅ VISION_API_KEY: {settings.VISION_API_KEY[:20]}...")
    else:
        print("❌ VISION_API_KEY: 未配置")
    
    # 检查GRAPHRAG_API_KEY
    if settings.GRAPHRAG_API_KEY:
        print(f"✅ GRAPHRAG_API_KEY: {settings.GRAPHRAG_API_KEY[:20]}...")
    else:
        print("❌ GRAPHRAG_API_KEY: 未配置")
    
    print()
    print("📋 配置来源验证:")
    
    # 验证配置是否从环境变量读取
    env_graphrag_key = os.getenv('GRAPHRAG_API_KEY')
    env_vision_key = os.getenv('VISION_API_KEY')
    
    if env_graphrag_key and env_graphrag_key == settings.GRAPHRAG_API_KEY:
        print("✅ GRAPHRAG_API_KEY 正确从环境变量读取")
    else:
        print("❌ GRAPHRAG_API_KEY 未从环境变量读取")
    
    if env_vision_key and env_vision_key == settings.VISION_API_KEY:
        print("✅ VISION_API_KEY 正确从环境变量读取")
    else:
        print("❌ VISION_API_KEY 未从环境变量读取")
    
    print()
    print("📋 配置一致性检查:")
    
    # 检查两个OpenAI key是否相同（应该相同）
    if settings.VISION_API_KEY == settings.GRAPHRAG_API_KEY:
        print("✅ VISION_API_KEY 和 GRAPHRAG_API_KEY 一致")
    else:
        print("⚠️  VISION_API_KEY 和 GRAPHRAG_API_KEY 不一致")
        print(f"   VISION_API_KEY: {settings.VISION_API_KEY[:20]}...")
        print(f"   GRAPHRAG_API_KEY: {settings.GRAPHRAG_API_KEY[:20]}...")
    
    print()
    print("📋 其他相关配置:")
    print(f"  - VISION_BASE_URL: {settings.VISION_BASE_URL}")
    print(f"  - VISION_MODEL: {settings.VISION_MODEL}")
    print(f"  - GRAPHRAG_API_BASE: {settings.GRAPHRAG_API_BASE}")
    print(f"  - GRAPHRAG_MODEL_NAME: {settings.GRAPHRAG_MODEL_NAME}")
    
    print("\n" + "=" * 60)
    print("🎯 统一配置测试完成")
    
    # 检查是否所有必要的配置都存在
    required_configs = [
        settings.VISION_API_KEY,
        settings.GRAPHRAG_API_KEY,
        settings.VISION_BASE_URL,
        settings.GRAPHRAG_API_BASE
    ]
    
    if all(required_configs):
        print("🎉 所有OpenAI相关配置都已正确设置！")
        print("✅ 现在只需要修改 llm_backend/.env 文件即可统一管理所有API Key")
        return True
    else:
        print("❌ 部分配置缺失，请检查.env文件")
        return False

if __name__ == "__main__":
    test_unified_config()
