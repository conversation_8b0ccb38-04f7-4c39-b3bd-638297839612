#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目专用Neo4j测试脚本
基于项目现有的Neo4j测试文件和配置
"""

import sys
import os
import traceback
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "llm_backend"))

def test_project_neo4j_notebook_style():
    """测试项目中的Neo4j Notebook风格连接"""
    print("\n" + "=" * 60)
    print("📓 测试项目Neo4j Notebook风格连接")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        # 从环境变量读取Neo4j连接配置
        import os
        from dotenv import load_dotenv
        load_dotenv('llm_backend/.env')

        NEO4J_URI = os.getenv('NEO4J_URL', "bolt://localhost:7687")
        NEO4J_USERNAME = os.getenv('NEO4J_USERNAME', "neo4j")
        NEO4J_PASSWORD = os.getenv('NEO4J_PASSWORD')
        NEO4J_DATABASE = os.getenv('NEO4J_DATABASE', "neo4j")
        
        print(f"📡 连接URI: {NEO4J_URI}")
        print(f"👤 用户名: {NEO4J_USERNAME}")
        print(f"🗄️ 数据库: {NEO4J_DATABASE}")
        
        # 创建驱动（模仿test.ipynb的方式）
        driver = GraphDatabase.driver(
            NEO4J_URI, 
            auth=(NEO4J_USERNAME, NEO4J_PASSWORD)
        )
        
        def test_connection():
            """内部连接测试函数（模仿notebook）"""
            with driver.session() as session:
                session.run("MATCH (n) RETURN n LIMIT 1")
        
        # 执行连接测试
        test_connection()
        print("✅ 连接成功！")
        
        # 额外的验证查询
        with driver.session() as session:
            result = session.run("RETURN 'Hello from Project Neo4j!' as message")
            record = result.single()
            print(f"📝 测试消息: {record['message']}")
            
            # 检查数据库状态
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = result.single()['node_count']
            print(f"📊 当前节点数量: {node_count}")
        
        driver.close()
        print("✅ 项目Neo4j Notebook风格测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 项目Neo4j Notebook风格测试失败: {e}")
        traceback.print_exc()
        return False

def test_neo4j_webserver_visualization():
    """测试Neo4j Web服务器可视化配置"""
    print("\n" + "=" * 60)
    print("🌐 测试Neo4j Web服务器可视化配置")
    print("=" * 60)
    
    try:
        # 检查项目中的neo4jvisualization.py配置
        viz_file = Path("llm_backend/app/graphrag/dev/webserver/scripts/neo4jvisualization.py")
        if viz_file.exists():
            print(f"✅ 找到可视化脚本: {viz_file}")
            
            # 读取配置
            with open(viz_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            print("📋 可视化脚本配置:")
            if "NEO4J_URI" in content:
                print("  ✅ 包含URI配置")
            if "NEO4J_USERNAME" in content:
                print("  ✅ 包含用户名配置")
            if "NEO4J_PASSWORD" in content:
                print("  ✅ 包含密码配置")
                
        else:
            print(f"⚠️ 可视化脚本不存在: {viz_file}")
        
        # 测试连接（使用可视化脚本的方式）
        from neo4j import GraphDatabase
        
        # 模仿可视化脚本的连接方式
        NEO4J_URI = "neo4j://localhost:7687"  # 注意这里用的是neo4j://而不是bolt://
        NEO4J_USERNAME = "neo4j"
        NEO4J_PASSWORD = "12369874c"
        
        try:
            driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USERNAME, NEO4J_PASSWORD))
            
            with driver.session() as session:
                result = session.run("RETURN 'Visualization connection test' as message")
                record = result.single()
                print(f"✅ 可视化连接测试成功: {record['message']}")
            
            driver.close()
            
        except Exception as e:
            print(f"⚠️ neo4j:// 协议连接失败，尝试bolt://协议: {e}")
            
            # 回退到bolt://协议
            NEO4J_URI = "bolt://localhost:7687"
            driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USERNAME, NEO4J_PASSWORD))
            
            with driver.session() as session:
                result = session.run("RETURN 'Visualization fallback test' as message")
                record = result.single()
                print(f"✅ 可视化回退连接测试成功: {record['message']}")
            
            driver.close()
        
        print("✅ Neo4j Web服务器可视化配置测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ Neo4j Web服务器可视化配置测试失败: {e}")
        traceback.print_exc()
        return False

def test_neo4j_data_import_readiness():
    """测试Neo4j数据导入准备情况"""
    print("\n" + "=" * 60)
    print("📊 测试Neo4j数据导入准备情况")
    print("=" * 60)
    
    try:
        # 检查数据导入脚本
        import_script = Path("llm_backend/app/graphrag/origin_data/create_neo4j_import.py")
        if import_script.exists():
            print(f"✅ 数据导入脚本存在: {import_script}")
        else:
            print(f"❌ 数据导入脚本不存在: {import_script}")
            return False
        
        # 检查导出数据目录
        data_dir = Path("llm_backend/app/graphrag/origin_data/exported_data")
        if data_dir.exists():
            print(f"✅ 导出数据目录存在: {data_dir}")
            
            # 列出CSV文件
            csv_files = list(data_dir.glob("*.csv"))
            print(f"📁 找到 {len(csv_files)} 个CSV文件:")
            for csv_file in csv_files[:10]:  # 只显示前10个
                print(f"  - {csv_file.name}")
            if len(csv_files) > 10:
                print(f"  ... 还有 {len(csv_files) - 10} 个文件")
                
        else:
            print(f"❌ 导出数据目录不存在: {data_dir}")
            return False
        
        # 检查Neo4j Admin导入目录
        admin_dir = Path("llm_backend/app/graphrag/origin_data/data/neo4j_admin")
        if admin_dir.exists():
            print(f"✅ Neo4j Admin导入目录存在: {admin_dir}")
            
            admin_files = list(admin_dir.glob("*.csv"))
            print(f"📁 找到 {len(admin_files)} 个Admin导入文件")
        else:
            print(f"⚠️ Neo4j Admin导入目录不存在: {admin_dir}")
            print("💡 可以运行数据导入脚本来生成")
        
        print("✅ Neo4j数据导入准备情况检查完成!")
        return True
        
    except Exception as e:
        print(f"❌ Neo4j数据导入准备情况测试失败: {e}")
        traceback.print_exc()
        return False

def test_neo4j_langchain_integration():
    """测试Neo4j与LangChain集成"""
    print("\n" + "=" * 60)
    print("🔗 测试Neo4j与LangChain集成")
    print("=" * 60)
    
    try:
        # 测试项目的LangChain Neo4j集成
        from app.lg_agent.kg_sub_graph.kg_neo4j_conn import get_neo4j_graph
        
        print("📦 导入项目Neo4j连接模块成功")
        
        # 创建Neo4j图实例
        neo4j_graph = get_neo4j_graph()
        print("✅ 项目Neo4j图实例创建成功")
        
        # 测试基本查询
        result = neo4j_graph.query("RETURN 'LangChain集成测试成功!' as message")
        print(f"📝 LangChain查询结果: {result}")
        
        # 测试Schema获取
        try:
            schema = neo4j_graph.get_schema
            print(f"📋 数据库Schema: {schema}")
        except Exception as e:
            print(f"⚠️ Schema获取失败（数据库可能为空）: {e}")
        
        # 测试刷新Schema
        try:
            neo4j_graph.refresh_schema()
            print("✅ Schema刷新成功")
        except Exception as e:
            print(f"⚠️ Schema刷新失败: {e}")
        
        print("✅ Neo4j与LangChain集成测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ Neo4j与LangChain集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_neo4j_basic_operations():
    """测试Neo4j基本操作"""
    print("\n" + "=" * 60)
    print("⚙️ 测试Neo4j基本操作")
    print("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            "bolt://localhost:7687", 
            auth=("neo4j", "12369874c")
        )
        
        with driver.session(database="neo4j") as session:
            # 清理测试数据
            session.run("MATCH (n:ProjectTest) DETACH DELETE n")
            
            # 创建测试节点
            session.run("""
                CREATE (n:ProjectTest {
                    name: '项目Neo4j测试节点',
                    created_at: datetime(),
                    test_type: 'project_specific',
                    test_id: 'proj_test_' + toString(rand())
                })
            """)
            print("✅ 创建项目测试节点成功")
            
            # 查询测试节点
            result = session.run("MATCH (n:ProjectTest) RETURN n.name as name, n.test_type as type")
            for record in result:
                print(f"📝 测试节点: {record['name']}, 类型: {record['type']}")
            
            # 创建关系测试
            session.run("""
                MATCH (n:ProjectTest)
                CREATE (n)-[:TESTED_IN_PROJECT]->(:TestResult {
                    status: 'success',
                    timestamp: datetime(),
                    project: 'fufan_deepseek_agent'
                })
            """)
            print("✅ 创建项目测试关系成功")
            
            # 查询关系
            result = session.run("""
                MATCH (n:ProjectTest)-[r:TESTED_IN_PROJECT]->(result:TestResult)
                RETURN n.name as node_name, result.project as project
            """)
            for record in result:
                print(f"🔗 关系查询: {record['node_name']} -> 项目: {record['project']}")
            
            # 清理测试数据
            session.run("MATCH (n:ProjectTest) DETACH DELETE n")
            session.run("MATCH (n:TestResult) DELETE n")
            print("🧹 清理测试数据完成")
        
        driver.close()
        print("✅ Neo4j基本操作测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ Neo4j基本操作测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 项目专用Neo4j测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now()}")
    print("=" * 80)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("项目Neo4j Notebook风格连接", test_project_neo4j_notebook_style),
        ("Neo4j Web服务器可视化配置", test_neo4j_webserver_visualization),
        ("Neo4j数据导入准备情况", test_neo4j_data_import_readiness),
        ("Neo4j与LangChain集成", test_neo4j_langchain_integration),
        ("Neo4j基本操作", test_neo4j_basic_operations),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 项目Neo4j测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有项目Neo4j测试都通过了！")
        print("\n💡 下一步建议:")
        print("  1. 运行数据导入脚本导入测试数据")
        print("  2. 测试知识图谱查询功能")
        print("  3. 集成到LangGraph工作流中")
        print("  4. 测试Neo4j可视化功能")
    else:
        print("⚠️ 部分测试失败，请检查配置和连接")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
