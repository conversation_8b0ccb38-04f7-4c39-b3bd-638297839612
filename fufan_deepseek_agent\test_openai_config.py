#!/usr/bin/env python3
"""
测试 OpenAI API 配置
"""
import os
import sys
sys.path.append('llm_backend')

from openai import OpenAI
from app.core.config import settings
import asyncio

def test_vision_api():
    """测试视觉模型 API"""
    print("🧪 测试视觉模型 API...")
    try:
        client = OpenAI(
            api_key=settings.VISION_API_KEY,
            base_url=settings.VISION_BASE_URL
        )
        
        # 简单的文本测试
        response = client.chat.completions.create(
            model=settings.VISION_MODEL,
            messages=[
                {"role": "user", "content": "Hello, this is a test message."}
            ],
            max_tokens=50
        )
        
        print(f"✅ 视觉模型 API 测试成功!")
        print(f"📋 模型: {settings.VISION_MODEL}")
        print(f"📋 响应: {response.choices[0].message.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ 视觉模型 API 测试失败: {e}")
        return False

def test_openai_embedding():
    """测试 OpenAI Embedding API"""
    print("\n🧪 测试 OpenAI Embedding API...")
    try:
        # 使用配置中的 OpenAI Key 测试 embedding
        client = OpenAI(
            api_key=settings.VISION_API_KEY,
            base_url=settings.VISION_BASE_URL
        )
        
        response = client.embeddings.create(
            model="text-embedding-3-small",
            input="This is a test text for embedding"
        )
        
        embedding = response.data[0].embedding
        print(f"✅ OpenAI Embedding API 测试成功!")
        print(f"📊 向量维度: {len(embedding)}")
        print(f"📊 向量前5个值: {embedding[:5]}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI Embedding API 测试失败: {e}")
        return False

def test_graphrag_config():
    """测试 GraphRAG 配置"""
    print("\n🧪 测试 GraphRAG 配置...")
    try:
        # 从配置中读取
        graphrag_api_key = settings.GRAPHRAG_API_KEY
        graphrag_api_base = settings.GRAPHRAG_API_BASE
        graphrag_model = settings.GRAPHRAG_MODEL_NAME

        if not graphrag_api_key or graphrag_api_key == "":
            print("❌ GRAPHRAG_API_KEY 配置为空")
            return False

        print(f"✅ GraphRAG 配置检查通过!")
        print(f"📋 API Base: {graphrag_api_base}")
        print(f"📋 Model: {graphrag_model}")
        print(f"📋 API Key: {graphrag_api_key[:20]}...")
        return True

    except Exception as e:
        print(f"❌ GraphRAG 配置检查失败: {e}")
        return False

def main():
    print("🚀 开始测试 OpenAI API 配置...")
    print("=" * 60)
    
    # 显示配置信息
    print("📋 当前配置:")
    print(f"  - 视觉模型 API Key: {settings.VISION_API_KEY[:20]}...")
    print(f"  - 视觉模型 Base URL: {settings.VISION_BASE_URL}")
    print(f"  - 视觉模型: {settings.VISION_MODEL}")
    print()
    
    success_count = 0
    total_tests = 3
    
    # 1. 测试视觉 API
    if test_vision_api():
        success_count += 1
    
    # 2. 测试 Embedding API
    if test_openai_embedding():
        success_count += 1
    
    # 3. 测试 GraphRAG 配置
    if test_graphrag_config():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有 OpenAI API 配置测试通过！")
        print("✅ 项目可以正常使用 OpenAI 相关功能")
    else:
        print("⚠️  部分测试失败，请检查配置")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
