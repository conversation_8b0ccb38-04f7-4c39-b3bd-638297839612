#!/usr/bin/env python3
"""
全面分析项目中所有配置是否统一管理
"""
import sys
import os
from pathlib import Path
sys.path.append('llm_backend')

from app.core.config import settings

def analyze_config_sources():
    """分析配置来源"""
    print("🔍 全面分析项目配置统一性...")
    print("=" * 80)
    
    # 检查主配置文件
    env_file_path = Path('llm_backend/.env')
    
    if not env_file_path.exists():
        print("❌ 主配置文件 llm_backend/.env 不存在")
        return False
    
    # 读取.env文件内容
    with open(env_file_path, 'r', encoding='utf-8') as f:
        env_content = f.read()
    
    print("📋 主配置文件分析:")
    print(f"✅ 主配置文件: {env_file_path}")
    
    # 分析各类配置
    config_categories = {
        "API Keys": [
            ("DEEPSEEK_API_KEY", "DeepSeek API密钥"),
            ("VISION_API_KEY", "OpenAI视觉API密钥"),
            ("GRAPHRAG_API_KEY", "GraphRAG API密钥"),
            ("SERPAPI_KEY", "SerpAPI搜索密钥")
        ],
        "数据库配置": [
            ("DB_HOST", "MySQL主机"),
            ("DB_PORT", "MySQL端口"),
            ("DB_USER", "MySQL用户名"),
            ("DB_PASSWORD", "MySQL密码"),
            ("DB_NAME", "MySQL数据库名")
        ],
        "Neo4j配置": [
            ("NEO4J_URL", "Neo4j连接URL"),
            ("NEO4J_USERNAME", "Neo4j用户名"),
            ("NEO4J_PASSWORD", "Neo4j密码"),
            ("NEO4J_DATABASE", "Neo4j数据库名")
        ],
        "Redis配置": [
            ("REDIS_HOST", "Redis主机"),
            ("REDIS_PORT", "Redis端口"),
            ("REDIS_PASSWORD", "Redis密码"),
            ("REDIS_DB", "Redis数据库")
        ],
        "安全配置": [
            ("SECRET_KEY", "JWT密钥")
        ]
    }
    
    all_unified = True
    
    for category, configs in config_categories.items():
        print(f"\n📂 {category}:")
        
        for env_var, description in configs:
            # 检查是否在.env文件中
            in_env_file = f"{env_var}=" in env_content
            
            # 检查是否在settings中有值
            try:
                setting_value = getattr(settings, env_var, None)
                has_value = setting_value is not None and setting_value != ""
            except:
                has_value = False
            
            if in_env_file and has_value:
                # 隐藏敏感信息
                if "KEY" in env_var or "PASSWORD" in env_var:
                    display_value = f"{str(setting_value)[:10]}..." if setting_value else "未设置"
                else:
                    display_value = str(setting_value)
                print(f"  ✅ {description}: {display_value}")
            elif in_env_file and not has_value:
                print(f"  ⚠️  {description}: 在.env中配置但值为空")
                all_unified = False
            elif not in_env_file and has_value:
                print(f"  ⚠️  {description}: 有值但不在.env文件中 (可能硬编码)")
                all_unified = False
            else:
                print(f"  ❌ {description}: 未配置")
                all_unified = False
    
    return all_unified

def check_hardcoded_configs():
    """检查硬编码配置"""
    print("\n" + "=" * 80)
    print("🔍 检查硬编码配置...")
    print("=" * 80)
    
    # 检查config.py中的硬编码默认值
    print("📋 config.py中的硬编码检查:")
    
    hardcoded_issues = []
    
    # 检查Neo4j默认密码
    if hasattr(settings, 'NEO4J_PASSWORD') and settings.NEO4J_PASSWORD == "password":
        hardcoded_issues.append("Neo4j密码使用默认值 'password'")
    
    # 检查JWT密钥
    if hasattr(settings, 'SECRET_KEY') and settings.SECRET_KEY == "your-secret-key":
        hardcoded_issues.append("JWT密钥使用默认值 'your-secret-key'")
    
    # 检查Redis密码
    if hasattr(settings, 'REDIS_PASSWORD') and settings.REDIS_PASSWORD == "":
        print("  ⚠️  Redis密码为空 (可能是有意的)")
    
    if hardcoded_issues:
        print("  ❌ 发现硬编码问题:")
        for issue in hardcoded_issues:
            print(f"    - {issue}")
        return False
    else:
        print("  ✅ 未发现明显的硬编码问题")
        return True

def check_multiple_config_files():
    """检查是否存在多个配置文件"""
    print("\n" + "=" * 80)
    print("🔍 检查多配置文件问题...")
    print("=" * 80)
    
    config_files = [
        "llm_backend/.env",
        "llm_backend/app/graphrag/data/.env",
        ".env.example",
        "llm_backend/app/graphrag/settings.yaml",
        "llm_backend/app/graphrag/settings_hybrid.yaml"
    ]
    
    active_configs = []
    deprecated_configs = []
    
    for config_file in config_files:
        file_path = Path(config_file)
        if file_path.exists():
            if config_file == "llm_backend/.env":
                active_configs.append(config_file)
            elif config_file == ".env.example":
                print(f"  📝 示例文件: {config_file} (正常)")
            else:
                # 检查是否包含实际配置
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "sk-" in content or "password" in content.lower():
                    deprecated_configs.append(config_file)
                else:
                    print(f"  📝 配置文件: {config_file} (无敏感信息)")
    
    print(f"\n📋 配置文件状态:")
    print(f"  ✅ 主配置文件: {len(active_configs)} 个")
    for config in active_configs:
        print(f"    - {config}")
    
    if deprecated_configs:
        print(f"  ⚠️  需要清理的配置文件: {len(deprecated_configs)} 个")
        for config in deprecated_configs:
            print(f"    - {config}")
        return False
    else:
        print(f"  ✅ 无需清理的配置文件")
        return True

def provide_recommendations():
    """提供配置优化建议"""
    print("\n" + "=" * 80)
    print("💡 配置优化建议")
    print("=" * 80)
    
    recommendations = [
        "1. 确保所有敏感配置都在 llm_backend/.env 文件中",
        "2. 移除代码中的硬编码密钥和密码",
        "3. 在生产环境中更改默认密码和密钥",
        "4. 定期轮换API密钥",
        "5. 使用强密码和复杂的JWT密钥",
        "6. 确保.env文件不被提交到版本控制系统",
        "7. 为不同环境(开发/测试/生产)使用不同的配置文件"
    ]
    
    for rec in recommendations:
        print(f"  {rec}")

def main():
    """主函数"""
    print("🚀 开始全面配置统一性分析...")
    
    # 分析配置来源
    configs_unified = analyze_config_sources()
    
    # 检查硬编码
    no_hardcoded = check_hardcoded_configs()
    
    # 检查多配置文件
    single_config = check_multiple_config_files()
    
    # 提供建议
    provide_recommendations()
    
    # 总结
    print("\n" + "=" * 80)
    print("🎯 配置统一性分析结果")
    print("=" * 80)
    
    if configs_unified and no_hardcoded and single_config:
        print("🎉 配置管理优秀！")
        print("✅ 所有配置都已统一管理")
        print("✅ 无硬编码问题")
        print("✅ 配置文件结构清晰")
    else:
        print("⚠️  配置管理需要改进:")
        if not configs_unified:
            print("  - 部分配置未统一到主配置文件")
        if not no_hardcoded:
            print("  - 存在硬编码配置问题")
        if not single_config:
            print("  - 存在多个配置文件需要清理")
    
    print("\n💡 建议: 定期运行此脚本检查配置一致性")

if __name__ == "__main__":
    main()
