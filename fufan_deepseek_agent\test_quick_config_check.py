#!/usr/bin/env python3
"""
快速检查主要配置文件中的硬编码问题
"""
import sys
import os
import re
from pathlib import Path
sys.path.append('llm_backend')

def check_specific_files():
    """检查特定的重要文件"""
    print("🔍 检查重要配置文件中的硬编码...")
    print("=" * 60)
    
    # 要检查的重要文件
    important_files = [
        'llm_backend/app/core/config.py',
        'test_mysql_connection.py',
        'test_neo4j_project_specific.py',
        'llm_backend/docs/10_deepseek_prompt_cache.ipynb',
        'llm_backend/app/graphrag/settings.yaml',
        'llm_backend/app/graphrag/settings_hybrid.yaml',
    ]
    
    # 敏感信息模式
    patterns = {
        'API Keys': r'sk-[a-zA-Z0-9]{20,}',
        'Passwords': r'password\s*[=:]\s*["\'][^"\']{3,}["\']',
        'Database URLs': r'mysql://[^"\s]+|bolt://[^"\s]+',
        'IP Addresses': r'192\.168\.\d+\.\d+:\d+',
    }
    
    issues_found = False
    
    for file_path in important_files:
        if not Path(file_path).exists():
            continue
            
        print(f"\n📁 检查: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except:
            print(f"  ❌ 无法读取文件")
            continue
        
        file_issues = []
        
        for category, pattern in patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                # 过滤明显的示例值
                if any(skip in match.lower() for skip in ['example', 'xxx', 'your-', 'placeholder']):
                    continue
                file_issues.append((category, match))
        
        if file_issues:
            issues_found = True
            for category, match in file_issues:
                # 隐藏敏感信息
                display = match[:15] + "..." if len(match) > 15 else match
                print(f"  ⚠️  {category}: {display}")
        else:
            print(f"  ✅ 无硬编码问题")
    
    return not issues_found

def check_config_defaults():
    """检查config.py中的默认值"""
    print(f"\n📋 检查config.py中的默认值...")
    
    config_file = Path('llm_backend/app/core/config.py')
    if not config_file.exists():
        print("❌ config.py不存在")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查可能有问题的默认值
    problematic_defaults = [
        ('NEO4J_PASSWORD.*=.*"password"', 'Neo4j默认密码'),
        ('SECRET_KEY.*=.*"your-secret-key"', 'JWT默认密钥'),
        ('REDIS_PASSWORD.*=.*""', 'Redis空密码'),
    ]
    
    issues = []
    for pattern, description in problematic_defaults:
        if re.search(pattern, content, re.IGNORECASE):
            issues.append(description)
    
    if issues:
        print("⚠️  发现问题的默认值:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ 默认值检查通过")
        return True

def check_test_files():
    """检查测试文件中的硬编码"""
    print(f"\n📋 检查测试文件中的硬编码...")
    
    test_files = [
        'test_mysql_connection.py',
        'test_neo4j_project_specific.py',
        'test_openai_config.py',
    ]
    
    issues_found = False
    
    for test_file in test_files:
        if not Path(test_file).exists():
            continue
            
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查硬编码的连接信息
        hardcoded_patterns = [
            r'password\s*=\s*["\'][^"\']{3,}["\']',
            r'NEO4J_PASSWORD\s*=\s*["\'][^"\']{3,}["\']',
            r'sk-[a-zA-Z0-9]{20,}',
        ]
        
        file_issues = []
        for pattern in hardcoded_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            file_issues.extend(matches)
        
        if file_issues:
            issues_found = True
            print(f"  ⚠️  {test_file}: 发现 {len(file_issues)} 个硬编码问题")
        else:
            print(f"  ✅ {test_file}: 无硬编码问题")
    
    return not issues_found

def check_env_consistency():
    """检查.env文件一致性"""
    print(f"\n📋 检查.env文件一致性...")
    
    main_env = Path('llm_backend/.env')
    example_env = Path('.env.example')
    
    if not main_env.exists():
        print("❌ 主.env文件不存在")
        return False
    
    if not example_env.exists():
        print("⚠️  .env.example文件不存在")
        return True
    
    # 读取两个文件的配置项
    with open(main_env, 'r', encoding='utf-8') as f:
        main_configs = set(re.findall(r'^([A-Z_]+)=', f.read(), re.MULTILINE))
    
    with open(example_env, 'r', encoding='utf-8') as f:
        example_configs = set(re.findall(r'^([A-Z_]+)=', f.read(), re.MULTILINE))
    
    # 检查差异
    missing_in_main = example_configs - main_configs
    missing_in_example = main_configs - example_configs
    
    if missing_in_main:
        print(f"⚠️  主.env文件缺少配置项: {', '.join(missing_in_main)}")
    
    if missing_in_example:
        print(f"⚠️  .env.example缺少配置项: {', '.join(missing_in_example)}")
    
    if not missing_in_main and not missing_in_example:
        print("✅ .env文件一致性良好")
        return True
    
    return False

def main():
    """主函数"""
    print("🚀 快速配置检查...")
    
    # 检查重要文件
    no_hardcoded = check_specific_files()
    
    # 检查默认值
    good_defaults = check_config_defaults()
    
    # 检查测试文件
    clean_tests = check_test_files()
    
    # 检查一致性
    consistent = check_env_consistency()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 快速检查结果")
    print("=" * 60)
    
    if no_hardcoded and good_defaults and clean_tests and consistent:
        print("🎉 配置检查通过！")
        print("✅ 无明显硬编码问题")
        print("✅ 默认值安全")
        print("✅ 测试文件干净")
        print("✅ 配置文件一致")
    else:
        print("⚠️  发现配置问题:")
        if not no_hardcoded:
            print("  - 重要文件中有硬编码")
        if not good_defaults:
            print("  - 默认值不安全")
        if not clean_tests:
            print("  - 测试文件有硬编码")
        if not consistent:
            print("  - 配置文件不一致")
    
    print("\n💡 建议:")
    print("  1. 将所有硬编码移到 llm_backend/.env")
    print("  2. 更新config.py中的默认值")
    print("  3. 清理测试文件中的硬编码")
    print("  4. 保持.env和.env.example一致")

if __name__ == "__main__":
    main()
